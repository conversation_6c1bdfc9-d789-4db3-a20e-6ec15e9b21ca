import type { DateRange } from "react-day-picker";

export interface CategoryFormData {
	name: string;
	description: string;
	color: string;
	conditions: CategoryCondition[];
}

export interface CategoryCondition {
	id: string;
	categoryCondition: string;
	conditionCheck?: string;
	field?: string;
	parameter?: string;
	parameterLabel?: string;
	dataSource?: string;
	dataSourceList?: string;
	selectedAnswer?: string;
	selectedDate?: Date;
	selectedDateRange?: DateRange;
	priorityLevel?: string;
	informationCheckType?: string;
	selectedForm?: string;
	selectedQuestion?: string;
	selectedFormAnswer?: string;
	selectedCustomIntake?: string;
	selectedCustomIntakeAnswer?: string;
}

export interface LocationSelectionData {
	applyToAll: boolean;
	selectedLocations: string[];
	selectedStations: Record<string, string[]>;
	locationSelections: Array<{
		location_id: number;
		all_stations: boolean;
		station_ids: number[];
	}>;
}
