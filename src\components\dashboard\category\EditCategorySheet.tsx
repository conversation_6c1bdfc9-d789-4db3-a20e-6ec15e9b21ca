import React, { useState, useRef, useEffect } from "react";
import { She<PERSON>, SheetContent } from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	X,
	PaintBucket,
	CircleMinus,
	CirclePlus,
	Check,
	CalendarIcon,
} from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import {
	Popover,
	PopoverContent,
	PopoverTrigger,
} from "@/components/ui/popover";
import { format } from "date-fns";
import { SketchPicker, type ColorResult } from "react-color";
import { Label } from "@/components/ui/label";
import { type DateRange } from "react-day-picker";
import { LocationSelectionStep } from "./LocationSelectionStep";
import { useUpdateCategory } from "@/hooks/useCategories";
import type {
	UpdateCategoryRequest,
	CategoryDetailData,
} from "@/lib/api/categoriesApi";
import { useOrganizationContext } from "@/features/organizations/context/OrganizationContext";
import { useForms, useCustomIntakes } from "@/hooks/useForms";
import { useLocations } from "@/features/locations/hooks/useLocations";
import type {
	CategoryFormData,
	CategoryCondition,
	LocationSelectionData,
} from "./types";

type ExtendedCategoryDetailData = CategoryDetailData;

interface EditCategorySheetProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	category?: ExtendedCategoryDetailData;
	onSubmit?: (data: CategoryFormData & LocationSelectionData) => void;
}

const conditionCheckOptions = [
	{ id: "registration", label: "Registration" },
	{ id: "last_visit", label: "Last Visit" },
	{ id: "priority", label: "Priority" },
	{ id: "information_check", label: "Information Check" },
];

const parameterOptions = {
	registration: [
		{ id: "before_date", label: "Before date" },
		{ id: "after_date", label: "After date" },
		{ id: "on_date", label: "On date" },
		{ id: "during_range", label: "During range" },
		{ id: "outside_range", label: "Outside range" },
	],
	last_visit: [
		{ id: "before_date", label: "Before date" },
		{ id: "after_date", label: "After date" },
		{ id: "on_date", label: "On date" },
		{ id: "during_range", label: "During range" },
		{ id: "outside_range", label: "Outside range" },
	],
};

const priorityLevels = [
	{ id: "high", label: "High" },
	{ id: "medium", label: "Medium" },
	{ id: "low", label: "Low" },
];

const colorPalette = [
	"#222A31",
	"#5E98C9",
	"#0277D8",
	"#9A76C9",
	"#32BA3F",
	"#E36F6F",
	"#E9ED18",
	"#FF9500",
	"#C77676",
	"#FA38C4",
	"#54758F",
	"#A3762D",
];

export function EditCategorySheet({
	open,
	onOpenChange,
	category,
	onSubmit,
}: EditCategorySheetProps) {
	const { organizationId } = useOrganizationContext();
	const [currentStep, setCurrentStep] = useState<
		"category" | "locations" | "success"
	>("category");
	const [formData, setFormData] = useState<CategoryFormData>({
		name: "",
		description: "",
		color: "#000000",
		conditions: [
			{
				id: "1",
				categoryCondition: "",
			},
		],
	});
	const [locationData, setLocationData] = useState<LocationSelectionData>({
		applyToAll: false,
		selectedLocations: [],
		selectedStations: {},
		locationSelections: [],
	});
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [showColorPicker, setShowColorPicker] = useState(false);
	const colorPickerRef = useRef<HTMLDivElement>(null);
	const {
		data: formsResponse,
		isLoading: isLoadingForms,
		error: formsError,
	} = useForms(organizationId);
	const {
		data: customIntakesResponse,
		isLoading: isLoadingCustomIntakes,
		error: customIntakesError,
	} = useCustomIntakes(organizationId);
	const {
		data: locationsResponse,
		isLoading: isLoadingLocations,
		error: locationsError,
	} = useLocations({}, organizationId || undefined);

	const getFormsData = () => {
		if (!formsResponse?.data) return [];
		return formsResponse.data.map((form) => ({
			id: form.form_id,
			label: form.form_title,
		}));
	};

	const getQuestionsForForm = (formId: string) => {
		if (!formsResponse?.data) return [];
		const form = formsResponse.data.find((f) => f.form_id === formId);
		if (!form) return [];
		return form.questions.map((question) => ({
			id: question.id,
			label: question.question,
		}));
	};

	const getAnswersForQuestion = (formId: string, questionId: string) => {
		if (!formsResponse?.data) return [];
		const form = formsResponse.data.find((f) => f.form_id === formId);
		if (!form) return [];
		const question = form.questions.find((q) => q.id === questionId);
		if (!question) return [];
		return question.options.map((option) => ({
			id: option.id,
			label: option.label,
		}));
	};

	const getCustomIntakesData = () => {
		if (!customIntakesResponse?.data) return [];
		return customIntakesResponse.data.map((intake) => ({
			id: intake.id.toString(),
			label: intake.title,
		}));
	};

	const getAnswersForCustomIntake = (intakeId: string) => {
		if (!customIntakesResponse?.data) return [];
		const intake = customIntakesResponse.data.find(
			(i) => i.id.toString() === intakeId
		);
		if (!intake) return [];
		return intake.options.map((option) => ({
			id: option.id.toString(),
			label: option.label,
		}));
	};

	const hasAvailableForms = () => {
		return !isLoadingForms && !formsError && getFormsData().length > 0;
	};

	const hasAvailableCustomIntakes = () => {
		return (
			!isLoadingCustomIntakes &&
			!customIntakesError &&
			getCustomIntakesData().length > 0
		);
	};

	const getInformationCheckOptions = () => {
		const options = [];

		if (hasAvailableForms()) {
			options.push({
				id: "selected_answers",
				label: "Selected Answers from Forms",
			});
		}

		if (hasAvailableCustomIntakes()) {
			options.push({ id: "custom_intakes", label: "Custom Intakes" });
		}

		return options;
	};

	const mapCategoryToFormData = (
		categoryData: ExtendedCategoryDetailData
	): CategoryFormData => {
		const baseFormData: CategoryFormData = {
			name: categoryData.name || "",
			description: categoryData.description || "",
			color: categoryData.color || "#000000",
			conditions: [
				{
					id: "1",
					categoryCondition:
						categoryData.type === "manual" ? "None" : "Custom",
				},
			],
		};
		if (
			categoryData.type === "conditional" &&
			categoryData.condition_type
		) {
			const condition: CategoryCondition = {
				id: "1",
				categoryCondition: "Custom",
			};
			if (categoryData.condition_type === "registration_date") {
				condition.conditionCheck = "registration";
			} else if (categoryData.condition_type === "last_seen") {
				condition.conditionCheck = "last_visit";
			} else if (categoryData.condition_type === "priority") {
				condition.conditionCheck = "priority";
				condition.priorityLevel = categoryData.conditional_value || "";
			} else if (categoryData.condition_type === "information_check") {
				condition.conditionCheck = "information_check";

				if (categoryData.information_type === "form_answer") {
					condition.informationCheckType = "selected_answers";
					condition.selectedForm = categoryData.form_question_id
						? getFormIdFromQuestionId(categoryData.form_question_id)
						: "";
					condition.selectedQuestion =
						categoryData.form_question_id || "";
					condition.selectedFormAnswer =
						categoryData.conditional_value || "";
				} else if (
					categoryData.information_type === "custom_intake_answer"
				) {
					condition.informationCheckType = "custom_intakes";
					condition.selectedCustomIntake =
						categoryData.custom_intake_id?.toString() || "";
					condition.selectedCustomIntakeAnswer =
						categoryData.conditional_value || "";
				}
			}

			if (
				(condition.conditionCheck === "registration" ||
					condition.conditionCheck === "last_visit") &&
				categoryData.operator
			) {
				if (categoryData.operator === "before") {
					condition.parameter = "before_date";
				} else if (categoryData.operator === "after") {
					condition.parameter = "after_date";
				} else if (categoryData.operator === "equals") {
					condition.parameter = "on_date";
				} else if (categoryData.operator === "between") {
					condition.parameter = "during_range";
				} else if (categoryData.operator === "outside_of_range") {
					condition.parameter = "outside_range";
				}

				// Store the operator label if available
				if (categoryData.operator_label) {
					condition.parameterLabel = categoryData.operator_label;
				}

				if (categoryData.date_range_start) {
					condition.selectedDate = new Date(
						categoryData.date_range_start
					);
				}
				if (
					categoryData.date_range_start &&
					categoryData.date_range_end
				) {
					condition.selectedDateRange = {
						from: new Date(categoryData.date_range_start),
						to: new Date(categoryData.date_range_end),
					};
				}
			}

			baseFormData.conditions = [condition];
		}

		return baseFormData;
	};

	const mapAppliedToLocationData = (
		appliedTo: Array<{ location: string; stations: string[] }>,
		locationsResponse: any[]
	): LocationSelectionData => {
		if (!appliedTo || appliedTo.length === 0) {
			return {
				applyToAll: false,
				selectedLocations: [],
				selectedStations: {},
				locationSelections: [],
			};
		}

		const selectedLocations: string[] = [];
		const selectedStations: Record<string, string[]> = {};
		const locationSelections: Array<{
			location_id: number;
			all_stations: boolean;
			station_ids: number[];
		}> = [];

		appliedTo.forEach((appliedLocation) => {
			const location = locationsResponse?.find(
				(loc) => loc.name === appliedLocation.location
			);

			if (location) {
				const locationId = location.id.toString();
				selectedLocations.push(locationId);
				if (appliedLocation.stations.length === 0) {
					selectedStations[locationId] = [];
					locationSelections.push({
						location_id: parseInt(locationId),
						all_stations: true,
						station_ids: [],
					});
				} else {
					const stationIds: number[] = [];
					const stationStringIds: string[] = [];

					appliedLocation.stations.forEach((stationName) => {
						const station = location.stations?.find(
							(s: any) => s.name === stationName
						);
						if (station) {
							stationIds.push(station.id);
							stationStringIds.push(station.id.toString());
						}
					});

					selectedStations[locationId] = stationStringIds;
					locationSelections.push({
						location_id: parseInt(locationId),
						all_stations: false,
						station_ids: stationIds,
					});
				}
			}
		});

		return {
			applyToAll: false,
			selectedLocations,
			selectedStations,
			locationSelections,
		};
	};

	const getFormIdFromQuestionId = (questionId: string): string => {
		if (!formsResponse?.data) return "";
		for (const form of formsResponse.data) {
			if (form.questions.some((q) => q.id === questionId)) {
				return form.form_id;
			}
		}
		return "";
	};

	const updateCategoryMutation = useUpdateCategory({
		onSuccess: () => {
			setCurrentStep("success");
		},
		onError: (error) => {
			console.error("Error updating category:", error);
		},
	});

	useEffect(() => {
		if (category && open) {
			const mappedFormData = mapCategoryToFormData(category);
			setFormData(mappedFormData);
			if (category.applied_to && locationsResponse) {
				const mappedLocationData = mapAppliedToLocationData(
					category.applied_to,
					locationsResponse
				);
				setLocationData(mappedLocationData);
			} else {
				setLocationData({
					applyToAll: false,
					selectedLocations: [],
					selectedStations: {},
					locationSelections: [],
				});
			}
		}
	}, [
		category,
		open,
		formsResponse,
		customIntakesResponse,
		locationsResponse,
	]);

	const resetForm = () => {
		setFormData({
			name: "",
			description: "",
			color: "#000000",
			conditions: [
				{
					id: "1",
					categoryCondition: "",
				},
			],
		});
		setLocationData({
			applyToAll: true,
			selectedLocations: [],
			selectedStations: {},
			locationSelections: [],
		});
		setCurrentStep("category");
	};

	const handleInputChange = (
		e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
	) => {
		const { name, value } = e.target;
		setFormData({
			...formData,
			[name]: value,
		});
	};

	const handleColorSelect = (color: string) => {
		setFormData({
			...formData,
			color,
		});
		setShowColorPicker(false);
	};

	const handleColorPickerChange = (color: ColorResult) => {
		setFormData({
			...formData,
			color: color.hex,
		});
	};

	const toggleColorPicker = () => {
		setShowColorPicker(!showColorPicker);
	};

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				colorPickerRef.current &&
				!colorPickerRef.current.contains(event.target as Node)
			) {
				setShowColorPicker(false);
			}
		};

		if (showColorPicker) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [showColorPicker]);

	const handleConditionChange = (
		conditionId: string,
		field: string,
		value: string | string[] | Date | DateRange | undefined
	) => {
		const stringValue = Array.isArray(value)
			? value[0]
			: typeof value === "string"
				? value
				: value;

		setFormData({
			...formData,
			conditions: formData.conditions.map((condition) => {
				if (condition.id === conditionId) {
					const updatedCondition = {
						...condition,
						[field]:
							field === "selectedDate" ||
							field === "selectedDateRange"
								? value
								: stringValue,
					};
					if (field === "categoryCondition") {
						updatedCondition.conditionCheck = "";
						updatedCondition.parameter = "";
						updatedCondition.parameterLabel = undefined;
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
						updatedCondition.priorityLevel = "";
						updatedCondition.informationCheckType = "";
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
					} else if (field === "conditionCheck") {
						updatedCondition.parameter = "";
						updatedCondition.parameterLabel = undefined;
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
						updatedCondition.priorityLevel = "";
						updatedCondition.informationCheckType = "";
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
					} else if (field === "parameter") {
						updatedCondition.selectedDate = undefined;
						updatedCondition.selectedDateRange = undefined;
						// Clear the stored parameter label when user changes parameter
						updatedCondition.parameterLabel = undefined;
					} else if (field === "informationCheckType") {
						updatedCondition.selectedForm = "";
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
						updatedCondition.selectedCustomIntake = "";
						updatedCondition.selectedCustomIntakeAnswer = "";
						if (
							stringValue === "selected_answers" &&
							!hasAvailableForms()
						) {
							updatedCondition.informationCheckType = "";
						} else if (
							stringValue === "custom_intakes" &&
							!hasAvailableCustomIntakes()
						) {
							updatedCondition.informationCheckType = "";
						}
					} else if (field === "selectedForm") {
						updatedCondition.selectedQuestion = "";
						updatedCondition.selectedFormAnswer = "";
					} else if (field === "selectedQuestion") {
						updatedCondition.selectedFormAnswer = "";
					} else if (field === "selectedCustomIntake") {
						updatedCondition.selectedCustomIntakeAnswer = "";
					}

					return updatedCondition;
				}
				return condition;
			}),
		});
	};

	const handleAddCondition = () => {
		if (formData.conditions.length < 5) {
			const newCondition: CategoryCondition = {
				id: Date.now().toString(),
				categoryCondition: "",
			};
			setFormData({
				...formData,
				conditions: [...formData.conditions, newCondition],
			});
		}
	};

	const handleRemoveCondition = (conditionId: string) => {
		if (formData.conditions.length > 1) {
			setFormData({
				...formData,
				conditions: formData.conditions.filter(
					(condition) => condition.id !== conditionId
				),
			});
		}
	};

	const transformToApiFormat = (
		formData: CategoryFormData,
		locationData: LocationSelectionData
	): UpdateCategoryRequest => {
		const isManualCategory = formData.conditions.every(
			(condition) =>
				condition.categoryCondition === "None" ||
				condition.categoryCondition === ""
		);

		console.log("Form conditions:", formData.conditions);
		console.log("Is manual category (None condition):", isManualCategory);
		console.log("Location data applyToAll:", locationData.applyToAll);

		const apiData: UpdateCategoryRequest = {
			id: category?.id || 0,
			name: formData.name,
			description: formData.description,
			color: formData.color,
			type: isManualCategory ? "manual" : "conditional",
		};
		if (locationData.applyToAll) {
			apiData.apply_to_all_locations = true;
		} else {
			apiData.location_selections = locationData.locationSelections;
			const allLocationIds: number[] = [];
			const allStationIds: number[] = [];

			locationData.locationSelections.forEach((selection) => {
				allLocationIds.push(selection.location_id);
				if (!selection.all_stations) {
					allStationIds.push(...selection.station_ids);
				}
			});

			apiData.location_ids = allLocationIds;
			apiData.station_ids = allStationIds;
		}

		if (isManualCategory) {
			apiData.client_ids = [];
		}

		if (!isManualCategory) {
			const customCondition = formData.conditions.find(
				(condition) => condition.categoryCondition === "Custom"
			);

			if (customCondition) {
				console.log("Processing custom condition:", customCondition);
				if (customCondition.conditionCheck === "registration") {
					apiData.condition_type = "registration_date";
				} else if (customCondition.conditionCheck === "last_visit") {
					apiData.condition_type = "last_seen";
				} else if (customCondition.conditionCheck === "priority") {
					apiData.condition_type = "priority";
					apiData.operator = "equals";
				} else if (
					customCondition.conditionCheck === "information_check"
				) {
					if (
						customCondition.informationCheckType ===
						"selected_answers"
					) {
						apiData.condition_type = "information_check";
						apiData.information_type = "form_answer";
						apiData.operator = "equals";
						if (customCondition.selectedQuestion) {
							console.log(
								"Selected question ID:",
								customCondition.selectedQuestion
							);
							apiData.form_question_id =
								customCondition.selectedQuestion;
						}
						if (customCondition.selectedFormAnswer) {
							apiData.conditional_value =
								customCondition.selectedFormAnswer;
						}
					} else if (
						customCondition.informationCheckType ===
						"custom_intakes"
					) {
						apiData.condition_type = "information_check";
						apiData.information_type = "custom_intake_answer";
						apiData.operator = "equals";
						if (customCondition.selectedCustomIntake) {
							apiData.custom_intake_id = parseInt(
								customCondition.selectedCustomIntake
							);
						}
						if (customCondition.selectedCustomIntakeAnswer) {
							apiData.conditional_value =
								customCondition.selectedCustomIntakeAnswer;
						}
					}
				}

				if (
					customCondition.conditionCheck === "registration" ||
					customCondition.conditionCheck === "last_visit"
				) {
					if (customCondition.parameter === "before_date") {
						apiData.operator = "before";
					} else if (customCondition.parameter === "after_date") {
						apiData.operator = "after";
					} else if (customCondition.parameter === "on_date") {
						apiData.operator = "equals";
					} else if (customCondition.parameter === "during_range") {
						apiData.operator = "between";
					} else if (customCondition.parameter === "outside_range") {
						apiData.operator = "outside_of_range";
					}
					if (customCondition.selectedDate) {
						apiData.date_range_start = customCondition.selectedDate
							.toISOString()
							.split("T")[0];
					}
					if (customCondition.selectedDateRange) {
						if (customCondition.selectedDateRange.from) {
							apiData.date_range_start =
								customCondition.selectedDateRange.from
									.toISOString()
									.split("T")[0];
						}
						if (customCondition.selectedDateRange.to) {
							apiData.date_range_end =
								customCondition.selectedDateRange.to
									.toISOString()
									.split("T")[0];
						}
					}
				}

				if (customCondition.conditionCheck === "priority") {
					if (customCondition.priorityLevel) {
						apiData.priority = customCondition.priorityLevel;
						apiData.conditional_value =
							customCondition.priorityLevel;
					}
				}
			}
		}

		return apiData;
	};

	const handleCategorySubmit = () => {
		if (!formData.name.trim()) {
			return;
		}
		setCurrentStep("locations");
	};

	const handleLocationSubmit = async () => {
		setIsSubmitting(true);
		try {
			const apiData = transformToApiFormat(formData, locationData);
			console.log("Updating category data:", apiData);
			console.log("Fields being sent:", Object.keys(apiData));
			await updateCategoryMutation.mutateAsync(apiData);
		} catch (error) {
			console.error("Error updating category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleSkipLocationSelection = async () => {
		setIsSubmitting(true);
		try {
			const skippedLocationData: LocationSelectionData = {
				applyToAll: true,
				selectedLocations: [],
				selectedStations: {},
				locationSelections: [],
			};
			const apiData = transformToApiFormat(formData, skippedLocationData);
			console.log("Updating category data (skipped location):", apiData);
			console.log("Fields being sent:", Object.keys(apiData));
			await updateCategoryMutation.mutateAsync(apiData);
		} catch (error) {
			console.error("Error updating category:", error);
		} finally {
			setIsSubmitting(false);
		}
	};

	const handleClose = () => {
		resetForm();
		onOpenChange(false);
	};

	const handleSheetOpenChange = (open: boolean) => {
		if (!open) {
			resetForm();
		}
		onOpenChange(open);
	};

	return (
		<Sheet open={open} onOpenChange={handleSheetOpenChange}>
			<SheetContent className="w-full !max-w-[525px] p-0 [&>button]:hidden">
				{currentStep === "success" ? (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Edit Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="flex flex-1 items-center justify-center">
							{renderSuccessContent()}
						</div>
					</div>
				) : (
					<div className="flex h-full max-h-screen flex-col">
						<div className="flex flex-shrink-0 flex-col gap-2.5 p-6 pb-0">
							<div className="flex h-14 items-start justify-start gap-2.5">
								<div className="flex flex-1 flex-col items-start justify-start gap-2">
									<div className="text-base leading-7 font-semibold">
										Edit Category
									</div>
									<div className="text-xs leading-none font-normal text-gray-500">
										Category information
									</div>
								</div>
								<div className="flex items-start justify-start gap-2.5">
									<Button
										variant="ghost"
										size="icon"
										onClick={handleClose}
										className="h-9 w-9 rounded-md"
									>
										<X className="h-4 w-4" />
									</Button>
								</div>
							</div>
						</div>
						<div className="min-h-0 flex-1 overflow-y-auto p-6 pt-6">
							{currentStep === "category" &&
								renderCategoryContent()}
							{currentStep === "locations" &&
								renderLocationContent()}
						</div>

						<div className="flex-shrink-0 bg-white p-6">
							{currentStep === "category"
								? renderCategoryFooter()
								: renderLocationFooter()}
						</div>
					</div>
				)}
			</SheetContent>
		</Sheet>
	);

	function renderCategoryContent() {
		return (
			<div className="flex flex-col gap-6">
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Category Details</h3>

					<div className="space-y-2">
						<Label htmlFor="name" className="text-xs font-medium">
							Category Name *
						</Label>
						<Input
							id="name"
							name="name"
							value={formData.name}
							onChange={handleInputChange}
							className="h-9 text-xs"
							placeholder="Enter category name"
						/>
					</div>

					<div className="space-y-2">
						<Label
							htmlFor="description"
							className="text-xs font-medium"
						>
							Description
						</Label>
						<Textarea
							id="description"
							name="description"
							value={formData.description}
							onChange={handleInputChange}
							className="min-h-20 resize-none text-xs"
							placeholder="Category Description"
						/>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Assign Color</h3>

					<div className="relative flex items-center gap-2">
						<button
							type="button"
							onClick={toggleColorPicker}
							className="flex h-9 items-center gap-4 rounded-md bg-gray-100 p-2 transition-colors hover:bg-gray-200"
						>
							<PaintBucket className="h-5 w-5" />
							<div
								className="h-6 w-6 rounded-md"
								style={{
									backgroundColor: formData.color,
								}}
							/>
						</button>
						<Input
							value={formData.color}
							onChange={(e) => handleColorSelect(e.target.value)}
							className="h-9 w-24 text-xs"
							placeholder="#000000"
						/>

						{showColorPicker && (
							<div
								ref={colorPickerRef}
								className="absolute bottom-full left-0 z-50 mb-2 border border-gray-200 bg-white shadow-lg"
								style={{ borderRadius: "8px" }}
							>
								<style>{`
									.sketch-picker-custom .flexbox-fix:last-child {
										display: none !important;
									}
									.sketch-picker-custom .flexbox-fix:nth-last-child(2) {
										display: none !important;
									}
								`}</style>
								<div className="sketch-picker-custom">
									<SketchPicker
										color={formData.color}
										onChange={handleColorPickerChange}
										disableAlpha={true}
										presetColors={[]}
									/>
								</div>
							</div>
						)}
					</div>

					<div className="flex flex-col gap-1.5">
						<p className="text-[10px] text-gray-500">Recent</p>
						<div className="flex flex-wrap gap-1.5">
							{colorPalette.map((color, index) => (
								<button
									key={index}
									type="button"
									onClick={() => handleColorSelect(color)}
									className={`relative h-7 w-7 rounded-md transition-all ${
										formData.color === color
											? "ring-2 ring-blue-500 ring-offset-1"
											: ""
									}`}
									style={{
										backgroundColor: color,
									}}
								>
									{formData.color === color && (
										<div className="absolute inset-0 flex items-center justify-center">
											<Check className="h-3.5 w-3.5 text-white" />
										</div>
									)}
								</button>
							))}
						</div>
					</div>
				</div>
				<div className="flex flex-col gap-4">
					<h3 className="text-sm font-semibold">Conditions</h3>

					{formData.conditions.map((condition) => (
						<div key={condition.id} className="flex flex-col gap-4">
							<div className="space-y-2">
								<Label className="text-xs font-medium">
									Category Condition *
								</Label>
								<Select
									value={condition.categoryCondition}
									onValueChange={(value) =>
										handleConditionChange(
											condition.id,
											"categoryCondition",
											value
										)
									}
								>
									<SelectTrigger className="mt-2 h-9 w-full text-xs">
										<SelectValue placeholder="Select condition" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="None">
											None
										</SelectItem>
										<SelectItem value="Custom">
											Custom
										</SelectItem>
									</SelectContent>
								</Select>
							</div>

							{condition.categoryCondition === "Custom" && (
								<div className="space-y-2">
									<Label className="text-xs font-medium">
										Condition Check *
									</Label>
									<Select
										value={condition.conditionCheck || ""}
										onValueChange={(value) =>
											handleConditionChange(
												condition.id,
												"conditionCheck",
												value
											)
										}
									>
										<SelectTrigger className="mt-2 h-9 w-full text-xs">
											<SelectValue placeholder="Select condition check" />
										</SelectTrigger>
										<SelectContent>
											{conditionCheckOptions.map(
												(option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												)
											)}
										</SelectContent>
									</Select>
								</div>
							)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Information Check Type *
										</Label>
										<Select
											value={
												condition.informationCheckType ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"informationCheckType",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select information check type" />
											</SelectTrigger>
											<SelectContent>
												{isLoadingForms ||
												isLoadingCustomIntakes ? (
													<SelectItem
														value="loading"
														disabled
													>
														Loading options...
													</SelectItem>
												) : getInformationCheckOptions()
														.length === 0 ? (
													<SelectItem
														value="no-options"
														disabled
													>
														No forms or custom
														intakes available
													</SelectItem>
												) : (
													getInformationCheckOptions().map(
														(option) => (
															<SelectItem
																key={option.id}
																value={
																	option.id
																}
															>
																{option.label}
															</SelectItem>
														)
													)
												)}
												]{" "}
												{!hasAvailableForms() &&
													!isLoadingForms && (
														<SelectItem
															value="forms-unavailable"
															disabled
														>
															Selected Answers
															from Forms (No forms
															available)
														</SelectItem>
													)}
												{!hasAvailableCustomIntakes() &&
													!isLoadingCustomIntakes && (
														<SelectItem
															value="intakes-unavailable"
															disabled
														>
															Custom Intakes (No
															custom intakes
															available)
														</SelectItem>
													)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"selected_answers" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Form *
										</Label>
										<Select
											value={condition.selectedForm || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedForm",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select form" />
											</SelectTrigger>
											<SelectContent>
												{getFormsData().map((form) => (
													<SelectItem
														key={form.id}
														value={form.id}
													>
														{form.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"selected_answers" &&
								condition.selectedForm && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Question *
										</Label>
										<Select
											value={
												condition.selectedQuestion || ""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedQuestion",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select question" />
											</SelectTrigger>
											<SelectContent>
												{getQuestionsForForm(
													condition.selectedForm
												).map((question) => (
													<SelectItem
														key={question.id}
														value={question.id}
													>
														{question.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"selected_answers" &&
								condition.selectedForm &&
								condition.selectedQuestion && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Answer *
										</Label>
										<Select
											value={
												condition.selectedFormAnswer ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedFormAnswer",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select answer" />
											</SelectTrigger>
											<SelectContent>
												{getAnswersForQuestion(
													condition.selectedForm,
													condition.selectedQuestion
												).map((answer) => (
													<SelectItem
														key={answer.id}
														value={answer.id}
													>
														{answer.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"custom_intakes" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Custom Intake *
										</Label>
										<Select
											value={
												condition.selectedCustomIntake ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedCustomIntake",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select custom intake" />
											</SelectTrigger>
											<SelectContent>
												{getCustomIntakesData().map(
													(intake) => (
														<SelectItem
															key={intake.id}
															value={intake.id}
														>
															{intake.label}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck ===
									"information_check" &&
								condition.informationCheckType ===
									"custom_intakes" &&
								condition.selectedCustomIntake && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Answer *
										</Label>
										<Select
											value={
												condition.selectedCustomIntakeAnswer ||
												""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"selectedCustomIntakeAnswer",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select answer" />
											</SelectTrigger>
											<SelectContent>
												{getAnswersForCustomIntake(
													condition.selectedCustomIntake
												).map((answer) => (
													<SelectItem
														key={answer.id}
														value={answer.id}
													>
														{answer.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								condition.conditionCheck === "priority" && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Priority Level *
										</Label>
										<Select
											value={
												condition.priorityLevel || ""
											}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"priorityLevel",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select priority level" />
											</SelectTrigger>
											<SelectContent>
												{priorityLevels.map(
													(option) => (
														<SelectItem
															key={option.id}
															value={option.id}
														>
															{option.label}
														</SelectItem>
													)
												)}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								(condition.conditionCheck === "registration" ||
									condition.conditionCheck ===
										"last_visit") && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Parameter *
										</Label>
										{/* Debug info */}
										<div className="mb-2 text-xs text-gray-500">
											Debug: parameter=
											{condition.parameter},
											parameterLabel=
											{condition.parameterLabel}
										</div>
										<Select
											value={condition.parameter || ""}
											onValueChange={(value) =>
												handleConditionChange(
													condition.id,
													"parameter",
													value
												)
											}
										>
											<SelectTrigger className="h-9 w-full text-xs">
												<SelectValue placeholder="Select parameter" />
											</SelectTrigger>
											<SelectContent>
												{parameterOptions[
													condition.conditionCheck as keyof typeof parameterOptions
												]?.map((option) => (
													<SelectItem
														key={option.id}
														value={option.id}
													>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								(condition.conditionCheck === "registration" ||
									condition.conditionCheck ===
										"last_visit") &&
								condition.parameter &&
								(condition.parameter === "before_date" ||
									condition.parameter === "after_date" ||
									condition.parameter === "on_date") && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Date *
										</Label>
										<Popover>
											<PopoverTrigger asChild>
												<Button
													variant="outline"
													className="h-9 w-full justify-start text-left text-xs font-normal"
												>
													<CalendarIcon className="mr-2 h-3 w-3" />
													{condition.selectedDate ? (
														format(
															condition.selectedDate,
															"PPP"
														)
													) : (
														<span>Pick a date</span>
													)}
												</Button>
											</PopoverTrigger>
											<PopoverContent
												className="w-auto p-0"
												align="start"
											>
												<Calendar
													mode="single"
													selected={
														condition.selectedDate
													}
													onSelect={(date) =>
														handleConditionChange(
															condition.id,
															"selectedDate",
															date
														)
													}
													initialFocus
												/>
											</PopoverContent>
										</Popover>
									</div>
								)}

							{condition.categoryCondition === "Custom" &&
								(condition.conditionCheck === "registration" ||
									condition.conditionCheck ===
										"last_visit") &&
								condition.parameter &&
								(condition.parameter === "during_range" ||
									condition.parameter ===
										"outside_range") && (
									<div className="space-y-2">
										<Label className="text-xs font-medium">
											Select Date Range *
										</Label>
										<Popover>
											<PopoverTrigger asChild>
												<Button
													variant="outline"
													className="h-9 w-full justify-start text-left text-xs font-normal"
												>
													<CalendarIcon className="mr-2 h-3 w-3" />
													{condition.selectedDateRange
														?.from ? (
														condition
															.selectedDateRange
															.to ? (
															<>
																{format(
																	condition
																		.selectedDateRange
																		.from,
																	"LLL dd, y"
																)}{" "}
																-{" "}
																{format(
																	condition
																		.selectedDateRange
																		.to,
																	"LLL dd, y"
																)}
															</>
														) : (
															format(
																condition
																	.selectedDateRange
																	.from,
																"LLL dd, y"
															)
														)
													) : (
														<span>
															Pick a date range
														</span>
													)}
												</Button>
											</PopoverTrigger>
											<PopoverContent
												className="w-auto p-0"
												align="start"
											>
												<Calendar
													initialFocus
													mode="range"
													defaultMonth={
														condition
															.selectedDateRange
															?.from
													}
													selected={
														condition.selectedDateRange
													}
													onSelect={(range) =>
														handleConditionChange(
															condition.id,
															"selectedDateRange",
															range
														)
													}
													numberOfMonths={2}
												/>
											</PopoverContent>
										</Popover>
									</div>
								)}

							{formData.conditions.length > 1 && (
								<div className="flex justify-end">
									<button
										type="button"
										onClick={() =>
											handleRemoveCondition(condition.id)
										}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CircleMinus className="h-3 w-3" />
										Remove Condition
									</button>
								</div>
							)}
						</div>
					))}

					{formData.conditions.length < 5 &&
						formData.conditions.some(
							(condition) =>
								condition.categoryCondition === "Custom"
						) && (
							<div className="flex items-center justify-between">
								<div className="flex flex-col gap-0.5">
									<button
										type="button"
										onClick={handleAddCondition}
										className="flex items-center gap-2 text-xs text-[#27272A]"
									>
										<CirclePlus className="h-3 w-3" />
										Add Another Condition
									</button>
									<div className="pl-5 text-[8px] text-gray-500">
										(Max 5)
									</div>
								</div>
							</div>
						)}
				</div>
			</div>
		);
	}

	function renderCategoryFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 px-4 text-xs"
				>
					Cancel
				</Button>
				<Button
					onClick={handleCategorySubmit}
					disabled={isSubmitting || !formData.name.trim()}
					className="h-9 bg-[#005893] px-4 text-xs hover:bg-[#004a7a]"
				>
					Next
				</Button>
			</div>
		);
	}

	function renderLocationContent() {
		return (
			<LocationSelectionStep
				locationData={locationData}
				onLocationDataChange={setLocationData}
			/>
		);
	}

	function renderLocationFooter() {
		return (
			<div className="flex items-center justify-end gap-3">
				<Button
					variant="outline"
					onClick={handleClose}
					className="h-9 rounded-md border border-gray-200 bg-white px-4 py-2 text-xs font-medium"
				>
					Cancel
				</Button>
				<div className="flex flex-1 items-center justify-end gap-3">
					<Button
						variant="secondary"
						onClick={handleSkipLocationSelection}
						className="h-9 rounded-md bg-gray-100 px-4 py-2 text-xs font-medium"
					>
						Skip for now
					</Button>
					<Button
						onClick={handleLocationSubmit}
						disabled={
							isSubmitting ||
							(!locationData.applyToAll &&
								locationData.selectedLocations.length === 0)
						}
						className="h-9 rounded-md px-4 py-2 text-xs font-medium text-white"
					>
						{isSubmitting ? "Saving..." : "Save"}
					</Button>
				</div>
			</div>
		);
	}

	function renderSuccessContent() {
		const handleDone = () => {
			try {
				const finalData = {
					...formData,
					...locationData,
				};
				onSubmit?.(finalData);
				resetForm();
				onOpenChange(false);
			} catch (error) {
				console.error("Error updating category:", error);
			}
		};

		return (
			<div className="flex flex-col items-center justify-center gap-8">
				<div className="flex flex-col items-center justify-start gap-11">
					<div className="inline-flex items-center justify-start gap-2.5 overflow-hidden rounded-[500px] bg-zinc-500/5 p-8">
						<div className="relative h-12 w-12 overflow-hidden">
							<Check
								className="absolute top-3 left-2 h-8 w-8 text-[#005893]/16"
								strokeWidth={4}
							/>
						</div>
					</div>

					<div className="flex w-full max-w-72 flex-col items-center justify-start gap-3">
						<div className="text-foreground text-center text-xl leading-loose font-semibold">
							Category Updated
						</div>
						<div className="text-muted-foreground text-center text-sm leading-tight font-normal">
							The category has been updated successfully.
						</div>
					</div>
				</div>

				<div className="inline-flex items-start justify-center gap-3">
					<Button
						onClick={handleDone}
						className="h-9 w-20 bg-sky-800 px-4 py-2 text-xs font-medium text-white hover:bg-sky-900"
					>
						Done
					</Button>
				</div>
			</div>
		);
	}
}
